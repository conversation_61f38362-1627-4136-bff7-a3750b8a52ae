package com.intelliquor.cloud.shop.terminal.controller.admin;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.intelliquor.cloud.shop.common.dao.DisplayResultCommonDao;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.exception.RestResponse;
import com.intelliquor.cloud.shop.common.model.DisplayResultModel;
import com.intelliquor.cloud.shop.common.service.ITerminalShopCommonService;
import com.intelliquor.cloud.shop.common.service.TerminalProductProtocolV2Service;
import com.intelliquor.cloud.shop.common.service.TerminalRewardCalculatorService;
import com.intelliquor.cloud.shop.common.service.resp.ProtocoTimeParamResp;
import com.intelliquor.cloud.shop.common.utils.ColumnConstant;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalRewardCalcReq;
import com.intelliquor.cloud.shop.terminal.model.req.TerminalRewardCalcRocketMqReq;
import com.intelliquor.cloud.shop.terminal.rocketmq.MqTestSendService;
import com.intelliquor.cloud.shop.terminal.service.IDisplayResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/rocketmq")
public class TerminalDisplayRewardCalcRocketMqController {

    @Value(value = "${rocketmq.producer.group}")
    private String group;

    @Autowired
    private TerminalProductProtocolV2Service terminalProductProtocolV2CommonService;
    @Autowired
    private TerminalRewardCalculatorService terminalRewardCalculatorService;

    @Autowired
    private DisplayResultCommonDao displayResultCommonDao;

    @Autowired
    private IDisplayResultService displayResultService;

    @Autowired
    private MqTestSendService mqTestSendService;
    @Value(value = "${rocketmq.producer.topic.test}")
    private String testTopic;

    @Autowired
    private ITerminalShopCommonService terminalShopCommonService;


    @GetMapping("/displayRewardCalc/sendMessage")
    public RestResponse<String> sendMessage(@RequestParam("year") String year, @RequestParam("quarter") Integer quarter, @RequestParam("password") String password) {
        log.info("统计陈列需要计算奖励的终端======开始执行");
        if(!password.equals("18951002")){
            log.info("统计陈列需要计算奖励密码：", password);
            return RestResponse.error("password error");
        }
//        LocalDate currentDate = LocalDate.now();
//        Month currentMonth = currentDate.getMonth();
//        int currentDay = currentDate.getDayOfMonth();
//        log.info("统计陈列需要计算奖励的终端======月份：", currentMonth);

        // 当前时间是每个季度的第一个月的1号
//        if (currentDay == 1 &&
//                (currentMonth == Month.JANUARY || currentMonth == Month.APRIL ||
//                        currentMonth == Month.JULY || currentMonth == Month.OCTOBER)) {
        log.info("统计陈列需要计算奖励的终端======进入执行逻辑,具体核算年份：{}，季度：{}",year,quarter);
//          2024年12月20日 金星要求调整为指定年季度奖励，原逻辑是计算上季度的奖励
//            String year = String.valueOf(currentDate.getYear());
//            //  Month 值从 1 开始，因此我们需要 +1 来获得当前月份
//            int month = currentDate.getMonthValue();
//            int quarter = ((month - 1) / 3) + 1;
//            if (quarter == 1) {
//                year = String.valueOf((Integer.valueOf(year) - 1));
//                quarter =  4;
//            } else {
//                quarter = quarter - 1;
//            }
            // 判断若是24年5季度则直接返回2024-01-01和2025-02-28
            //ProtocoTimeParamResp displayProtocoTimeParamResp = terminalProductProtocolV2CommonService.getDisplayTimeByProtocolTypeAndYearAndQuarter(year, quarter);
            ProtocoTimeParamResp displayProtocoTimeParamResp = new ProtocoTimeParamResp();
            if( quarter == ColumnConstant.FIVE_QUARTER ) {
                displayProtocoTimeParamResp = terminalProductProtocolV2CommonService.setDateRange("2024-01-01", "2025-02-20");
            } else {
                displayProtocoTimeParamResp = terminalProductProtocolV2CommonService.getDisplayTimeByProtocolTypeAndYearAndQuarter(year, quarter);
            }

            //遍历并核算奖励列表
            displayResultService.asyncCalculateDisplayReward(displayProtocoTimeParamResp.getStartDate(), displayProtocoTimeParamResp.getEndDate(), year, quarter);
//          批量使用该方式
//            List<TerminalProductProtocolRelationModel> displayRewardTerminals = terminalProductProtocolV2CommonService.getProtocolTerminalIdsByProtocolType(ProtocolTypeEnum.DISPLAY.getKey(),  displayProtocoTimeParamResp.getStartDate(), displayProtocoTimeParamResp.getEndDate());

//          单条排查使用该方式
//            Long relationId = 13031L;
//            12741L\13031L\14134\16882\16884
//            List<TerminalProductProtocolRelationModel> displayRewardTerminals = new ArrayList<>();
//            List<TerminalProductProtocolRelationModel> displayRewardTerminals1 = terminalProductProtocolV2CommonService.getProtocolTerminalIdsByProtocolType(ProtocolTypeEnum.DISPLAY.getKey(),  displayProtocoTimeParamResp.getStartDate(),  displayProtocoTimeParamResp.getEndDate());
//            for (TerminalProductProtocolRelationModel item : displayRewardTerminals1) {
//                if(item.getId().longValue() == relationId.longValue()){
//                    displayRewardTerminals.add(item);
//                }
//            }


            //if(CollectionUtils.isNotEmpty(displayRewardTerminals)){
            //    log.info("统计陈列需要计算奖励的终端======开始，终端奖励结果{}个", displayRewardTerminals.size());
            //
            //    String batchNumber = terminalRewardCalculatorService.generateBatchNumber();
            //
            //    //遍历并核算奖励列表
            //    displayResultService.asyncCalculateDisplayReward(displayRewardTerminals, batchNumber, year, quarter, TerminalRewardOperationTypeEnum.INCENTIVE_ACCOUNTING.getKey());
            //
            //}else{
            //    log.info("统计陈列需要计算奖励的终端======结束，无终端需要计算奖励");
            //}
//        }
        return RestResponse.success("send message success");
    }

    @PostMapping("/displayRewardSupplementCalc/sendMessage")
    public RestResponse<String> sendMessage( @RequestBody TerminalRewardCalcRocketMqReq terminalRewardCalcRocketMqReq) {
        log.info("统计陈列需要补充计算奖励的终端======开始执行");
        String year = terminalRewardCalcRocketMqReq.getYear();
        Integer quarter = terminalRewardCalcRocketMqReq.getQuarter();
        log.info("统计包量需要补充计算奖励的年份：{}，季度：{}",year,quarter);
//        LocalDate currentDate = LocalDate.now();
//        Month currentMonth = currentDate.getMonth();
//        int currentDay = currentDate.getDayOfMonth();
//        log.info("统计陈列需要补充计算奖励的终端======月份：", currentMonth);

        // 当前时间是每个季度的第一个月的1号
//        if (currentDay == 1 &&
//                (currentMonth == Month.JANUARY || currentMonth == Month.APRIL ||
//                        currentMonth == Month.JULY || currentMonth == Month.OCTOBER)) {
        log.info("统计陈列需要补充计算奖励的终端======进入执行逻辑");

//        String year = String.valueOf(currentDate.getYear());
//        //  Month 值从 1 开始，因此我们需要 +1 来获得当前月份
//        int month = currentDate.getMonthValue();
//        int quarter = ((month - 1) / 3) + 1;
//        if (quarter == 1) {
//            year = String.valueOf((Integer.valueOf(year) - 1));
//            quarter =  4;
//        } else {
//            quarter = quarter - 1;
//        }

        //      查询终端该季度是否已经核算过奖励
        List<Long> terminalShopIds = terminalRewardCalcRocketMqReq.getTerminalShopIds();
        List<Long> terminalShopCalcIds = new ArrayList<>();
        for (Long terminalShopId : terminalShopIds) {
            DisplayResultModel displayResultModel = displayResultCommonDao.selectOne(new LambdaQueryWrapper<DisplayResultModel>()
                    .eq(DisplayResultModel::getIsDelete, DeleteFlagEnum.NOT_DELETE.getKey())
                    .eq(DisplayResultModel::getDisplayYear, year)
                    .eq(DisplayResultModel::getDisplayQuarter, quarter)
                    .eq(DisplayResultModel::getProtocolTypeNew, ProtocolTypeEnum.DISPLAY.getKey())
                    .eq(DisplayResultModel::getProtocolStandard, ProtocolStandardByYearEnum.STANDARD_24.getKey())
                    .eq(DisplayResultModel::getCycleType, CycleTypeEnum.QUARTER.getKey())
                    .eq(DisplayResultModel::getTerminalShopId, terminalShopId));
            if(Objects.nonNull(displayResultModel)){
                terminalShopCalcIds.add(terminalShopId);
            }
        }
        if(CollectionUtils.isNotEmpty(terminalShopCalcIds)) {
            log.info("统计陈列需要补充计算奖励的终端======结束，之前核算过终端奖励的终端共{}个，具体有以下终端{}", terminalShopCalcIds.size(),terminalShopCalcIds);
            return RestResponse.error("统计陈列需要补充计算奖励的终端======结束，之前核算过终端奖励的终端共" +terminalShopCalcIds.size()+"个,具体有以下终端:"+terminalShopCalcIds);
        }

        // 查询出来多少未激活，已合并的分店终端 terminalShopIds
        Map<String, List<Integer>> stringListMap = terminalShopCommonService.filterNotActiveMergedSubShopTerminal(terminalShopIds);
        List<Integer> notActiveShopIds = stringListMap.get("filterStatusEq0TerminalShopIds");
        List<Integer> subShopIds = stringListMap.get("filterSubShopIds");
        // 未激活、分店终端不做计算
        terminalShopIds.removeAll(notActiveShopIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList()));
        terminalShopIds.removeAll(subShopIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList()));
        //遍历并核算奖励列表
        // 判断若是24年5季度则直接返回2024-01-01和2025-02-28
        //ProtocoTimeParamResp displayProtocoTimeParamResp = terminalProductProtocolV2CommonService.getDisplayTimeByProtocolTypeAndYearAndQuarter(year, quarter);
        ProtocoTimeParamResp displayProtocoTimeParamResp = new ProtocoTimeParamResp();
        if( quarter == ColumnConstant.FIVE_QUARTER ) {
            displayProtocoTimeParamResp = terminalProductProtocolV2CommonService.setDateRange("2024-01-01", "2025-02-20");
        } else {
            displayProtocoTimeParamResp = terminalProductProtocolV2CommonService.getDisplayTimeByProtocolTypeAndYearAndQuarter(year, quarter);
        }
        displayResultService.asyncSupplementCalculateDisplayReward(displayProtocoTimeParamResp.getStartDate(), displayProtocoTimeParamResp.getEndDate(), terminalShopIds, year, quarter);

        if(CollectionUtils.isNotEmpty(notActiveShopIds)){
            log.info("统计陈列补算未执行未激活的终端共{}个，具体有以下终端{}", notActiveShopIds.size(),notActiveShopIds);
            return RestResponse.success("统计陈列补算未执行未激活的终端共"+notActiveShopIds.size()+"个，具体有以下终端"+notActiveShopIds);
        }
        if(CollectionUtils.isNotEmpty(subShopIds)){
            log.info("统计陈列补算未执行已合并的分店终端共{}个，具体有以下终端{}", subShopIds.size(),subShopIds);
            return RestResponse.success("统计陈列补算未执行已合并的分店终端共"+subShopIds.size()+"个，具体有以下终端"+subShopIds);
        }
        //List<TerminalProductProtocolRelationModel> displayRewardTerminals = new ArrayList<>();
        //for (Long terminalShopId : terminalShopIds) {
        //    List<TerminalProductProtocolRelationModel> terminalProductProtocolRelationList = terminalProductProtocolV2CommonService.getProtocolTerminalIdsByProtocolTypeAndTerminalShopId(ProtocolTypeEnum.DISPLAY.getKey(),  displayProtocoTimeParamResp.getStartDate(),  displayProtocoTimeParamResp.getEndDate(),terminalShopId);
        //    if (CollectionUtils.isNotEmpty(terminalProductProtocolRelationList)) {
        //        displayRewardTerminals.add(terminalProductProtocolRelationList.get(0));
        //    }
        //}


        //if(CollectionUtils.isNotEmpty(displayRewardTerminals)){
        //    log.info("统计陈列需要补充计算奖励的终端======开始，终端奖励结果{}个", displayRewardTerminals.size());
        //
        //    String batchNumber = terminalRewardCalculatorService.generateBatchNumber();
        //
        //    //遍历并核算奖励列表
        //    displayResultService.asyncCalculateDisplayReward(displayRewardTerminals, batchNumber, year, quarter, TerminalRewardOperationTypeEnum.SUPPLEMENTARY_CALCULATION.getKey());
        //
        //    return RestResponse.success("统计陈列需要补充计算奖励的终端======结束，批次号:"+batchNumber+"，终端奖励结果"+displayRewardTerminals.size()+"个，具体有以下终端："+displayRewardTerminals.stream().map(TerminalProductProtocolRelationModel::getTerminalShopId).collect(Collectors.toList()));
        //}else{
        //    log.info("统计陈列需要补充计算奖励的终端======结束，无终端需要计算奖励");
        //    return RestResponse.success("统计陈列需要补充计算奖励的终端结束，无终端需要计算奖励");
        //}
        return RestResponse.success("send message success");
//        }
    }

    @GetMapping("/callTest")
    public RestResponse<String> callTest(String text, HttpServletRequest request) {
        log.info("================callTest调用接口测试开始====================，参数：{}", text);
        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        // 打印调用时间和调用ip
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String strDate = formatter.format(new Date());
        log.info("callTest调用接口测试时间：{}, IP地址：{}", strDate, ipAddress);
        return RestResponse.success("callTest success");
    }

    @GetMapping("/callTestMq")
    public RestResponse<String> callTestMq(String text, HttpServletRequest request) {
        log.info("================callTestMq调用接口测试开始====================参数：{}", text);
        String ipAddress = request.getHeader("x-forwarded-for");
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
            ipAddress = request.getRemoteAddr();
        }

        // 打印调用时间和调用ip
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String strDate = formatter.format(new Date());
        log.info("callTestMq调用接口测试时间：{}, IP地址：{}", strDate, ipAddress);
        mqTestSendService.send(text, testTopic, group);
        return RestResponse.success("callTestMq success");
    }

    @PostMapping("/protocolRewardCalc/sendMessage")
    public RestResponse<String> sendMessage(@RequestBody TerminalRewardCalcReq terminalRewardCalcReq) {
        log.info("核算奖励开始，参数：{}", JSON.toJSONString(terminalRewardCalcReq));
        Long rewardCycleConfigId = terminalRewardCalcReq.getRewardCycleConfigId();
        List<Integer> rewardType = terminalRewardCalcReq.getRewardTypeList();
        if(ObjectUtils.isEmpty(rewardCycleConfigId)){
            log.error("核算周期配置id不能为空");
            return RestResponse.error("核算周期配置id不能为空");
        }
        if(CollectionUtils.isEmpty(rewardType)){
            log.error("奖励类型不能为空");
            return RestResponse.error("奖励类型不能为空");
        }
        rewardType.stream().filter(i -> i != ProtocolRewardTypeEnum.QUARTERLY_PACKAGE_REWARD.getKey() && i != ProtocolRewardTypeEnum.DISPLAY_REWARD.getKey() && i != ProtocolRewardTypeEnum.YEAR_PACKAGE_REWARD.getKey()).findAny().ifPresent(i -> {
            throw new BusinessException("奖励类型:"+ i +"不存在");
        });
        displayResultService.asyncCalculateReward(terminalRewardCalcReq);
        return RestResponse.success("send message success");
    }
}
