<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.intelliquor.cloud.shop.common.dao.CloudDealerOutBalanceMapper">
    <update id="updateOrderStatus">
        update t_cloud_dealer_out_balance set receiving_count = receiving_count+#{receivingCount},receiving_status = #{receivingStatus}
        <if test="receivingDate != null"> ,receiving_date=#{receivingDate} </if>
        where id= #{id}
    </update>
    <update id="updateReceivingStatus">
        update t_cloud_dealer_out_balance set receiving_status = #{receivingStatus} where id= #{id}
    </update>

    <update id="addNum">
        update t_cloud_dealer_out_balance set bottle_num = bottle_num + #{bottleNum} where id= #{id}
    </update>

    <select id="outBoundLog" resultType="com.intelliquor.cloud.shop.common.model.resp.CloudDealerOutBalanceResp">
        select b.*, s.name as shop_name, d.goods_code,d.goods_name, d.quantity from t_cloud_dealer_out_balance b left join t_cloud_dealer_out_balance_detail d on b.id = d.balance_id
        left join t_member_shop s on b.to_dealer_code = s.dealer_code
        <where>
            b.is_delete = 0 and b.status=0 and b.bottle_num>0
            <if test="fromDealerCode != null and fromDealerCode != ''">
                and b.from_dealer_code = #{fromDealerCode}
            </if>
            <if test="toDealerCode != null and toDealerCode != ''">
                and b.to_dealer_code = #{toDealerCode}
            </if>
            <if test="keyword != null and keyword != ''">
                and (b.transaction like concat('%',#{keyword},'%') or s.name like concat('%',#{keyword},'%'))
            </if>
            <if test="startTime != null and startTime != ''">
                and b.create_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                and b.create_time &lt;= #{endTime}
            </if>
            <if test="receivingStatus != null">
                and b.receiving_status = #{receivingStatus}
            </if>
        </where>
        order by b.id desc
    </select>
    <select id="selectManageList" resultType="com.intelliquor.cloud.shop.common.model.CloudDealerOutBalance">
        SELECT * FROM t_cloud_dealer_out_balance t WHERE t.status = 0 AND t.bottle_num>0 AND t.is_delete = 0 AND t.receiving_status = #{req.receivingStatus}
        AND t.to_dealer_code IN
        <foreach collection="dealerCodeList" item="dealerCode" open="(" close=")" separator=",">
            #{dealerCode}
        </foreach>
        <if test="req.name != null and req.name != ''">
            and t.transaction like concat('%',#{req.name},'%')
        </if>

        AND t.create_time <![CDATA[>]]> '2023-06-22 00:00:00'
        AND t.id IN (SELECT balance_id FROM t_cloud_dealer_out_balance_detail WHERE goods_code IN ('10030683','10030703','10013474','10030199',
                                                                                                   '10030635','10030669','10011357','10012526',
                                                                                                   '10030431','10030430','10030656','10030434',
                                                                                                   '10004545','10004546','10030702','10030693','10030891','10030916','10031353','10031343','10031351',
                                                                                                   '10030702','10030693','10030890','10030872','10030871','10031037','10031081','10030638','10030867',
                                                                                                   '10031074','10030754'))
        ORDER BY create_time DESC
    </select>
    <select id="selectBalanceList" resultType="com.intelliquor.cloud.shop.common.model.resp.CloudDealerOutBalanceResp">
        SELECT * FROM t_cloud_dealer_out_balance t WHERE t.status = 0 AND t.bottle_num>0 AND t.is_delete = 0  AND t.to_dealer_code= #{req.toDealerCode}
        AND t.create_time <![CDATA[>]]> '2023-06-22 00:00:00'
        <if test="req.transaction != null and req.transaction != ''">
            and t.transaction like concat('%',#{req.transaction},'%')
        </if>
        <if test="req.receivingStatus != null">
            and t.receiving_status = #{req.receivingStatus}
        </if>
        order by create_time desc
    </select>
    <select id="selectByOutNo" resultType="com.intelliquor.cloud.shop.common.model.CloudDealerOutBalance">
        SELECT * FROM t_cloud_dealer_out_balance t WHERE t.transaction = #{transaction} AND is_delete = 0 and bottle_num>0
    </select>
</mapper>
