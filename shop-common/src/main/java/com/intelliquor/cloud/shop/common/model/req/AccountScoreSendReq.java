package com.intelliquor.cloud.shop.common.model.req;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableList;
import com.intelliquor.cloud.shop.common.dao.TerminalScanDetailPlusDao;
import com.intelliquor.cloud.shop.common.enums.ActivityRewardSubTypeEnum;
import com.intelliquor.cloud.shop.common.enums.ActivityType;
import com.intelliquor.cloud.shop.common.enums.MemberScoreTransactionTypeCommEnum;
import com.intelliquor.cloud.shop.common.exception.BusinessException;
import com.intelliquor.cloud.shop.common.model.ActivityRewardRecordModel;
import com.intelliquor.cloud.shop.common.model.ExtendDataBean;
import com.intelliquor.cloud.shop.common.productCategory.service.IProductCategoryService;
import com.intelliquor.cloud.shop.common.service.IConsumerScanDetailCommonService;
import com.intelliquor.cloud.shop.common.service.TerminalScanDetailCommonService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2025-04-14 10:08
 */
@Data
@Slf4j
@JSONType(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
public class AccountScoreSendReq {

    /**
     * 终端id
     */
    @JSONField(name = "member_id")
    @JsonProperty("member_id")
    private String memberId;

    /**
     * 事项来源类型
     */
    @JSONField(name = "transaction_source")
    @JsonProperty("transaction_source")
    private String transactionSource;

    /**
     * 积分
     */
    @JsonProperty("points")
    private BigDecimal points;

    /**
     * 关联商品
     */
    @JSONField(name = "product")
    @JsonProperty("product")
    private List<Product> product;

    /**
     * 二维码信息
     */
    @JSONField(name = "qr_code")
    @JsonProperty("qr_code")
    private String qrCode;

    /**
     * 交易时间
     */
    @JSONField(name = "transaction_time")
    @JsonProperty("transaction_time")
    private String transactionTime;

    /**
     * 业务备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 业务单号
     */
    @JSONField(name = "business_no")
    @JsonProperty("business_no")
    private String businessNo;

    /**
     * 业务来源
     */
    @JSONField(name = "business_source")
    @JsonProperty("business_source")
    private String businessSource;

    /**
     * 扩展信息容器
     */
    @JSONField(name = "ext")
    @JsonProperty("ext")
    private Ext ext;


    /**
     * product类
     */
    @Data
    @JSONType(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    public static class Product {
        /**
         * Product SKU
         */
        @JSONField(name = "product_sku")
        @JsonProperty("product_sku")
        private String productSku;

        /**
         * Product name
         */
        @JSONField(name = "product_name")
        @JsonProperty("product_name")
        private String productName;
    }

    @Data
    public static class ProductCategory {
        /**
         * 产品品类
         */
        private String policyCategory;

        /**
         * 产品品类id
         */
        private String policyCategoryId;
    }


    /**
     * 扩展字段类说明
     * 产品品类
     */
    @Data
    @JSONType(serialzeFeatures = SerializerFeature.DisableCircularReferenceDetect)
    public static class Ext {
        /**
         * 关联业务单号
         */
        @JSONField(name = "order_id")
        @JsonProperty("order_id")
        private String orderId;


        /**
         * 门店/终端编码
         */
        @JSONField(name = "store_code")
        @JsonProperty("store_code")
        private String storeCode;


        /**
         * qrcode
         */
        @JSONField(name = "qrcode")
        @JsonProperty("qrcode")
        private String qrcode;

        /**
         * 产品编码
         */
        private List<Product> products;

        /**
         * 订单编号
         */
        private String orderNo;

        /**
         * 宴席单号
         */
        private String banquetNo;

        /**
         * 产品品类
         */
        private List<ProductCategory> productCategories;
    }


    /**
     * 创建基础构建器实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 创建奖励记录构建器实例
     */
    public static RewardRecordBuilder fromRewardRecord(@NotNull ActivityRewardRecordModel recordModel) {
        return new RewardRecordBuilder(recordModel);
    }

    /**
     * 为了向后兼容，保留原有的buildFrom方法
     */
    public static AccountScoreSendReq buildFrom(@NotNull ActivityRewardRecordModel recordModel) {
        return fromRewardRecord(recordModel).build();
    }

    /**
     * 为了向后兼容，保留原有的带productCategoryService参数的buildFrom方法
     */
    public static AccountScoreSendReq buildFrom(@NotNull ActivityRewardRecordModel recordModel, @NotNull IProductCategoryService productCategoryService) {
        return fromRewardRecord(recordModel)
                .withProductCategoryService(productCategoryService)
                .build();
    }

    /**
     * 基础构建器类
     */
    public static class Builder {
        private final AccountScoreSendReq request;

        private Builder() {
            this.request = new AccountScoreSendReq();
            this.request.setProduct(new ArrayList<>());
            this.request.setExt(new Ext());
        }

        public Builder memberId(String memberId) {
            this.request.setMemberId(memberId);
            return this;
        }

        public Builder transactionSource(String transactionSource) {
            this.request.setTransactionSource(transactionSource);
            return this;
        }

        public Builder points(BigDecimal points) {
            this.request.setPoints(points);
            return this;
        }

        public Builder product(List<Product> product) {
            this.request.setProduct(product);
            return this;
        }

        public Builder qrCode(String qrCode) {
            this.request.setQrCode(qrCode);
            return this;
        }

        public Builder transactionTime(String transactionTime) {
            this.request.setTransactionTime(transactionTime);
            return this;
        }

        public Builder remark(String remark) {
            this.request.setRemark(remark);
            return this;
        }

        public Builder businessNo(String businessNo) {
            this.request.setBusinessNo(businessNo);
            return this;
        }

        public Builder businessSource(String businessSource) {
            this.request.setBusinessSource(businessSource);
            return this;
        }

        public Builder ext(Ext ext) {
            this.request.setExt(ext);
            return this;
        }

        public AccountScoreSendReq build() {
            // 设置默认交易时间如果未指定
            if (StringUtils.isBlank(request.getTransactionTime())) {
                request.setTransactionTime(DateUtil.format(new Date(), DatePattern.UTC_WITH_XXX_OFFSET_FORMAT));
            }
            return request;
        }
    }

    /**
     * 奖励记录构建器类
     */
    public static class RewardRecordBuilder {
        private final ActivityRewardRecordModel recordModel;
        private IProductCategoryService productCategoryService;
        private TerminalScanDetailPlusDao terminalScanDetailPlusDao;
        private IConsumerScanDetailCommonService consumerScanDetailCommonService;

        private RewardRecordBuilder(@NotNull ActivityRewardRecordModel recordModel) {
            this.recordModel = Objects.requireNonNull(recordModel, "recordModel不能为null");
        }

        public RewardRecordBuilder withProductCategoryService(@NotNull IProductCategoryService productCategoryService) {
            this.productCategoryService = productCategoryService;
            return this;
        }

        public RewardRecordBuilder withTerminalScanDetailPlusDao(@NotNull TerminalScanDetailPlusDao terminalScanDetailPlusDao) {
            this.terminalScanDetailPlusDao = terminalScanDetailPlusDao;
            return this;
        }

        public RewardRecordBuilder withConsumerScanDetailCommonService(@NotNull IConsumerScanDetailCommonService consumerScanDetailCommonService) {
            this.consumerScanDetailCommonService = consumerScanDetailCommonService;
            return this;
        }

        /**
         * 解析扩展数据，保证返回非空的 ExtendDataBean
         */
        private ExtendDataBean parseExtendData(@NotNull ActivityRewardRecordModel recordModel) {
            return Optional.ofNullable(recordModel.getExtendData())
                    .filter(StringUtils::isNotBlank)
                    .map(json -> {
                        try {
                            return JSONObject.parseObject(json, ExtendDataBean.class);
                        } catch (Exception e) {
                            log.warn("解析扩展数据失败: {}", e.getMessage());
                            return new ExtendDataBean();
                        }
                    })
                    .orElseGet(ExtendDataBean::new);
        }

        /**
         * 构建商品信息列表
         */
        private List<Product> getProductInfo(@NotNull ActivityRewardRecordModel recordModel) {
            ExtendDataBean data = parseExtendData(recordModel);
            return Optional.ofNullable(data)
                    .filter(d -> StringUtils.isNotBlank(d.getGoodsCode()) || StringUtils.isNotBlank(d.getGoodsName()))
                    .map(d -> {
                        Product p = new Product();
                        p.setProductSku(d.getGoodsCode());
                        p.setProductName(d.getGoodsName());
                        return Collections.singletonList(p);
                    })
                    .orElseGet(()->{
                        // 根据activityType查询不同表获取商品信息
                        if (recordModel.getActivityType() == 1 && terminalScanDetailPlusDao != null) {
                            // 查询terminal_scan_detail表
                            return Optional.ofNullable(terminalScanDetailPlusDao.selectById(recordModel.getOriginId()))
                                    .map(detail -> {
                                        Product p = new Product();
                                        p.setProductSku(detail.getGoodsCode());
                                        p.setProductName(detail.getGoodsName());
                                        return Collections.singletonList(p);
                                    })
                                    .orElse(Collections.emptyList());
                        } else if (recordModel.getActivityType() == 2 && consumerScanDetailCommonService != null) {
                            // 查询consumer_scan_detail表
                            return Optional.ofNullable(consumerScanDetailCommonService.getById(recordModel.getOriginId()))
                                    .map(detail -> {
                                        Product p = new Product();
                                        p.setProductSku(detail.getGoodsCode());
                                        p.setProductName(detail.getGoodsName());
                                        return Collections.singletonList(p);
                                    })
                                    .orElse(Collections.emptyList());
                        }
                        return Collections.emptyList();
                    });
        }

        /**
         * 构建扩展信息，包括商品和产品分类
         */
        private Ext getExtInfo(ExtendDataBean data) {
            Ext ext = new Ext();
            ext.setProducts(getProductInfo(data));
            ext.setQrcode(recordModel.getCode());
            if (StringUtils.isNotBlank(data.getGoodsCode()) && productCategoryService != null) {
                String code = data.getGoodsCode();
                List<String> skus = ImmutableList.of(code);
                List<com.intelliquor.cloud.shop.common.productCategory.entity.ProductCategory> productCategories =
                        productCategoryService.findBySkus(skus);
                if (productCategories != null && !productCategories.isEmpty()) {
                    List<ProductCategory> categoryList = productCategories.stream()
                            .map(category -> {
                                ProductCategory productCategory = new ProductCategory();
                                productCategory.setPolicyCategory(category.getPolicyCategory());
                                productCategory.setPolicyCategoryId(category.getPolicyCategoryId());
                                return productCategory;
                            })
                            .collect(java.util.stream.Collectors.toList());
                    ext.setProductCategories(categoryList);
                    log.info("成功设置产品分类信息到ext中，共{}个分类", categoryList.size());
                } else {
                    log.warn("未找到商品编码{}的产品分类信息", data.getGoodsCode());
                }
            }
            return ext;
        }

        public AccountScoreSendReq build() {
            ExtendDataBean extendDataBean = parseExtendData(recordModel);

            return builder()
                    .memberId(String.valueOf(recordModel.getShopId()))
                    .transactionSource(determineTransactionSource(recordModel, extendDataBean))
                    .points(Optional.ofNullable(recordModel.getIntegral())
                            .map(BigDecimal::abs)
                            .orElse(BigDecimal.ZERO))
                    .transactionTime(DateUtil.format(new Date(), DatePattern.UTC_WITH_XXX_OFFSET_FORMAT))
                    .remark(recordModel.getSceneRemark())
                    .businessNo(recordModel.getBusinessId())
                    .qrCode(recordModel.getCode())
                    .businessSource("t_activity_reward_record表中的business_id")
                    .product(getProductInfo(extendDataBean))
                    .ext(getExtInfo(extendDataBean))
                    .build();
        }

        private String determineTransactionSource(ActivityRewardRecordModel recordModel, ExtendDataBean extendDataBean) {
            String rewardSubType;
            if (Objects.equals(recordModel.getActivityType(), ActivityType.SALE_REWARD.getCode())) {
                rewardSubType = Optional.ofNullable(extendDataBean)
                        .map(ExtendDataBean::getRewardSubType)
                        .orElse(ActivityRewardSubTypeEnum.DYNAMIC_MARKET_NORMAL_REWARD.getCode());
            } else if (Objects.equals(recordModel.getActivityType(), ActivityType.OPEN_BOTTLE_REWARD.getCode())) {
                rewardSubType = Optional.ofNullable(extendDataBean)
                        .map(ExtendDataBean::getRewardSubType)
                        .orElse(ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode());
            } else {
                log.info("无法识别的活动类型");
                throw new BusinessException("无法识别的活动类型");
            }

            MemberScoreTransactionTypeCommEnum transactionType = MemberScoreTransactionTypeCommEnum.getByEventType(
                    recordModel.getEventType(), rewardSubType);

            if (Objects.isNull(transactionType)) {
                log.info("无法获取到事项来源类型，事件类型: {}, 奖励子类型: {}", recordModel.getEventType(), rewardSubType);
                throw new BusinessException("无法获取到事项来源类型，事件类型: " + recordModel.getEventType() + ", 奖励子类型: " + rewardSubType);
            }

            return transactionType.name();
        }
    }

}
