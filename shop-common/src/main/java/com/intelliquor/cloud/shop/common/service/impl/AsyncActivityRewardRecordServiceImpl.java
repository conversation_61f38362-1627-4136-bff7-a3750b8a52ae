package com.intelliquor.cloud.shop.common.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.intelliquor.cloud.shop.common.constant.RedisConstant;
import com.intelliquor.cloud.shop.common.dao.*;
import com.intelliquor.cloud.shop.common.enums.*;
import com.intelliquor.cloud.shop.common.model.*;
import com.intelliquor.cloud.shop.common.model.req.AccountScoreSendReq;
import com.intelliquor.cloud.shop.common.productCategory.service.IProductCategoryService;
import com.intelliquor.cloud.shop.common.rocketmq.client.TerminalScoreAccountClient;
import com.intelliquor.cloud.shop.common.service.AsyncActivityRewardRecordService;
import com.intelliquor.cloud.shop.common.service.ICloudDealerActivityNewCommonService;
import com.intelliquor.cloud.shop.common.service.IConsumerScanDetailCommonService;
import com.intelliquor.cloud.shop.common.service.ZtBookRewardRecordService;
import com.intelliquor.cloud.shop.common.utils.DateUtils;
import com.intelliquor.cloud.shop.common.utils.KafkaUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncActivityRewardRecordServiceImpl implements AsyncActivityRewardRecordService {

    private final ActivityRewardRecordDao activityRewardRecordDao;

    private final RedisTemplate<String, Object> redisTemplate;

    private final TerminalScanDetailPlusDao terminalScanDetailDao;

    private final TerminalScanDetailPlusDao terminalScanDetailPlusDao;

    private final RewardReasonDao rewardReasonDao;

    private final CloudDealerRewardRecordDao cloudDealerRewardRecordDao;

    private final TerminalRewardRecordNewDao terminalRewardRecordNewDao;

    private final ZtBookRewardRecordService ztBookRewardRecordService;

    private final ICloudDealerActivityNewCommonService cloudDealerActivityNewCommonService;

    private final IProductCategoryService productCategoryService;

    @Resource
    private KafkaUtils kafkaUtils;

    @Resource
    private TerminalScoreAccountClient terminalScoreAccountClient;

    private final IConsumerScanDetailCommonService consumerScanDetailCommonService;

    /**
     * 处理实物奖励数据
     * @param model
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void handleInKind(ActivityRewardRecordModel model) {
        List<String> qrcodes = new ArrayList<>();
        qrcodes.add(model.getCode());
        //判断是否有重复的开瓶码 如果有把重复的码设置成失败
        List<ActivityRewardRecordModel> rewardRecordModelList = activityRewardRecordDao.selectByEntityQrcodeCount(qrcodes);
        if(!rewardRecordModelList.isEmpty()){
            List<String> cfCodes = rewardRecordModelList.stream().map(ActivityRewardRecordModel::getCode).collect(Collectors.toList());
            if(!cfCodes.isEmpty()){
                activityRewardRecordDao.updateSendStatusByEntityCode(cfCodes);
            } else {
                model.setSendStatus(4);
                activityRewardRecordDao.updateById(model);
            }
        }
    }

    /**
     * 处理开瓶奖励
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void handleOpenReward(ActivityRewardRecordModel activityRewardRecordModel) {
        ExtendDataBean extendDataBean = JSONObject.parseObject(activityRewardRecordModel.getExtendData(), ExtendDataBean.class);
        //奖励重复处理判断
        String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_BOTTLE_KEY, activityRewardRecordModel.getId());
        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, activityRewardRecordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
        if (!aBoolean) {
            return;
        }

        TerminalScanDetailModel terminalScanDetailModel = terminalScanDetailDao.selectById(extendDataBean.getScanDetailId());

        try {
            if (activityRewardRecordModel.getEventType().equals(EventType.DEALER_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.DEALER_FENCE_VIOLATE_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.DEALER_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
                // 处理经销商的开瓶奖励
                handleDealerBottleReward(activityRewardRecordModel, terminalScanDetailModel);
            }
            if (activityRewardRecordModel.getEventType().equals(EventType.TERMINAL_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.MEMBER_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.TERMINAL_FENCE_VIOLATE_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.MEMBER_FENCE_VIOLATE_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.TERMINAL_BANQUET_OPEN_BOTTLE_REWARD_DEDUCT.getCode())
                    || activityRewardRecordModel.getEventType().equals( EventType.TERMINAL_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.MEMBER_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT.getCode())
            ) {
                //处理终端的开瓶奖励
                handleTerminalBottleReward(activityRewardRecordModel, terminalScanDetailModel);
            }
            if (activityRewardRecordModel.getEventType().equals(EventType.DISTRIBUTOR_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.DISTRIBUTOR_FENCE_VIOLATE_OPEN_BOTTLE_REWARD.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.DISTRIBUTOR_OPEN_BOTTLE_REWARD_OPERATIONAL_REISSUE.getCode())
                    || activityRewardRecordModel.getEventType().equals(EventType.DISTRIBUTOR_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
                //处理分销商的开瓶奖励
                handleDistributorBottleReward(activityRewardRecordModel, terminalScanDetailModel);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理开瓶奖励失败:" + e.getMessage(), e);
        }
    }

    /**
     *  处理动销奖励
     * @param model
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void handleDxReward(ActivityRewardRecordModel model) {
        if (model.getEventType().equals(EventType.DEALER_SALE_REWARD.getCode())
                || model.getEventType().equals(EventType.DEALER_VIOLATE_SALE_PUNISH.getCode())
                || model.getEventType().equals(EventType.DEALER_VIOLATE_SALE_REFUND.getCode())
                || model.getEventType().equals(EventType.DEALER_SALE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
            //经销商的动销奖励
            List<ActivityRewardRecordModel> dealerRecordList = new ArrayList<>();
            dealerRecordList.add(model);
            // 处理经销商的动销奖励
            try {
                handleDealerSaleReward(dealerRecordList);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("处理经销商的动销奖励失败:" + e.getMessage(), e);
            }
        } else if (model.getEventType().equals(EventType.DISTRIBUTOR_SALE_REWARD.getCode())
                ||model.getEventType().equals(EventType.DISTRIBUTOR_VIOLATE_OPEN_BOTTLE_PUNISH.getCode())
                ||model.getEventType().equals(EventType.DISTRIBUTOR_VIOLATE_OPEN_BOTTLE_REFUND.getCode())
                || model.getEventType().equals(EventType.DISTRIBUTOR_SALE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
            //分销商动销奖励
            List<ActivityRewardRecordModel> distributorRecordList = new ArrayList<>();
            distributorRecordList.add(model);
            //处理分销商的奖励
            try {
                handleDistributorSaleReward(distributorRecordList);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("处理分销商的奖励失败:" + e.getMessage(), e);
            }
        } else if (model.getEventType().equals(EventType.TERMINAL_SALE_REWARD.getCode())
                || model.getEventType().equals(EventType.TERMINAL_VIOLATE_OPEN_BOTTLE_PUNISH.getCode())
                || model.getEventType().equals(EventType.TERMINAL_VIOLATE_OPEN_BOTTLE_REFUND.getCode())
                || model.getEventType().equals(EventType.TERMINAL_SALE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
            //终端动销奖励
            List<ActivityRewardRecordModel> terminalRecordList = new ArrayList<>();
            terminalRecordList.add(model);
            //处理终端的动销奖励
            try {
                handleTerminalSaleReward(terminalRecordList, false);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("处理终端的动销奖励失败:" + e.getMessage(), e);
            }
        } else if (model.getEventType().equals(EventType.MEMBER_SAlE_REWARD.getCode())
                || model.getEventType().equals(EventType.MEMBER_VIOLATE_OPEN_BOTTLE_PUNISH.getCode())
                || model.getEventType().equals(EventType.MEMBER_VIOLATE_OPEN_BOTTLE_REFUND.getCode())
                || model.getEventType().equals(EventType.MEMBER_SALE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
            //会员动销奖励
            List<ActivityRewardRecordModel> memberRecordList = new ArrayList<>();
            memberRecordList.add(model);
            //处理会员的动销奖励
            try {
                handleTerminalSaleReward(memberRecordList, true);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("处理会员的动销奖励失败:" + e.getMessage(), e);
            }
        }
    }

    /**
     * 判断同一个码,是否被其他终端收过货未退
     */
    private Boolean getJudgeDealerInfo(TerminalScanDetailModel detail) {
        Boolean rtnVal = false;
        TerminalScanDetailModel detailModel = terminalScanDetailPlusDao.hasJudgeTerminalRrwordByQrCode(detail.getId(), detail.getQrcode());
        if (Objects.nonNull(detailModel)) {
            RewardReason rewardReason = initRewardReason(detailModel, 0);
            rewardReason.setIsSave(true);
            rewardReason.setHasDealer(0);
            String msg = "码:" + detail.getQrcode() + "被" + detailModel.getReceivedOrderCode() + "收过货且已发奖励";
            rewardReason.setDealerDesc(msg);
            rewardReason.setHasDistributor(0);
            rewardReason.setDistributorDesc(msg);
            if (rewardReason.getIsSave()) {
                rewardReasonDao.insert(rewardReason);
            }
            rtnVal = true;
        }
        return rtnVal;
    }

    @Transactional
    public void handleDealerBottleReward(ActivityRewardRecordModel recordModel, TerminalScanDetailModel detail) {
        //码信息重复奖励判断
        LambdaQueryWrapper<CloudDealerRewardRecordModel> clqw = Wrappers.lambdaQuery();
        clqw.eq(CloudDealerRewardRecordModel::getOrderCode, recordModel.getOrderCode());
        clqw.eq(CloudDealerRewardRecordModel::getQrCode, recordModel.getCode());
        clqw.eq(CloudDealerRewardRecordModel::getOriginId, recordModel.getOriginId());
        clqw.eq(CloudDealerRewardRecordModel::getOriginType, 2);
        clqw.eq(CloudDealerRewardRecordModel::getIsDelete, 0);

        List<CloudDealerRewardRecordModel> cloudDealerRewardRecordModelListInDb = cloudDealerRewardRecordDao.selectList(clqw);
        if (CollUtil.isNotEmpty(cloudDealerRewardRecordModelListInDb)) {
            // 判断事件类型是否已经存在
            boolean alreadyExisted = false;

            // 相同的事件类型存在的情况下，需要检查奖励子类型是否已经存在
            if (checkExistedDuplicateEventType(recordModel)) {
                alreadyExisted = checkExistedRewardSubType(recordModel, cloudDealerRewardRecordModelListInDb);
            }

            // 3. 如果存在重复记录，更新当前记录状态并返回
            if (alreadyExisted) {
                recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
                String currentTime = DateUtils.convert2StringYYYYMMddHHmmss(new Date());
                recordModel.setSendMsg(recordModel.getSendMsg() + ",此码重复发放奖励:" + currentTime);
                activityRewardRecordDao.updateById(recordModel);
                return;
            }
        }

        //扩展字段
        ExtendDataBean extendDataBean = JSONObject.parseObject(recordModel.getExtendData(), ExtendDataBean.class);
        //组装数据
        CloudDealerRewardRecordModel insertData = new CloudDealerRewardRecordModel();
        if (Objects.nonNull(extendDataBean)) {
            String activityName = extendDataBean.getActivityName();
            insertData.setActivityName(activityName);
            insertData.setGoodCode(extendDataBean.getGoodsCode());
            insertData.setGoodName(extendDataBean.getGoodsName());
            insertData.setDealerCode(extendDataBean.getDealerCode());
            insertData.setShopId(extendDataBean.getDeliveryShopId());
            insertData.setUnitPriceOfRewardAmount(extendDataBean.getUnitAmount());
        }
        insertData.setGoodNumber(1);
        insertData.setOriginId(recordModel.getOriginId().longValue());
        insertData.setOriginType(2);
        insertData.setQrCode(recordModel.getCode());
        insertData.setActivityId(recordModel.getActivityId());
        insertData.setRewardType(1);
        insertData.setUnitPriceOfRewardAmount(recordModel.getIntegral());
        insertData.setRewardAmount(recordModel.getIntegral());
        insertData.setOrderCode(recordModel.getOrderCode());
        insertData.setIsDelete(0);
        insertData.setCreateTime(new Date());
        insertData.setCompanyId(detail.getCompanyId());
        insertData.setDetailId(recordModel.getOriginId());
        insertData.setStatus(1);
        cloudDealerRewardRecordDao.insert(insertData);
        recordModel.setSendStatus(SendStatus.SUCCESS_TYPE.getCode());
        recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
        recordModel.setSendMsg("发放奖励成功:" + DateUtils.convert2StringYYYYMMddHHmmss(new Date()));
        extendDataBean.setOldRewardId(insertData.getId());
        recordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
        activityRewardRecordDao.updateById(recordModel);

        ZtBookRewardRecordModel ztBookRewardRecordModel = ztBookRewardRecordService.getOne(new QueryWrapper<ZtBookRewardRecordModel>().lambda().eq(ZtBookRewardRecordModel::getBusinessId, recordModel.getBusinessId()).eq(ZtBookRewardRecordModel::getCallState, 1).eq(ZtBookRewardRecordModel::getIsDelete, 0).orderByDesc(ZtBookRewardRecordModel::getCreateTime).last("limit 1"));
        if (Objects.nonNull(ztBookRewardRecordModel)) {
            ztBookRewardRecordModel.setCallState(2);
            ztBookRewardRecordModel.setUpdateTime(new Date());
            ztBookRewardRecordService.updateById(ztBookRewardRecordModel);
        }
    }

    private boolean checkExistedRewardSubType(ActivityRewardRecordModel recordModel, List<CloudDealerRewardRecordModel> cloudDealerRewardRecordModelListInDb) {
        // 1. 检查奖励子类型是否重复
        Set<Integer> activityIds = cloudDealerRewardRecordModelListInDb.stream()
                .map(CloudDealerRewardRecordModel::getActivityId)
                .collect(Collectors.toSet());

        if (!activityIds.isEmpty()) {
            return cloudDealerActivityNewCommonService.listByIds(activityIds).stream()
                    .map(DealerActivityNewCommonModel::getRewardSubType)
                    .map(rewardSubType -> StringUtils.isBlank(rewardSubType) ?
                            ActivityRewardSubTypeEnum.OPEN_BOTTLE_NORMAL_REWARD.getCode() : rewardSubType)
                    .anyMatch(rewardSubType -> Objects.equals(rewardSubType, recordModel.getRewardSubType()));
        }
        return false;
    }

    private void handleTerminalBottleReward(ActivityRewardRecordModel recordModel, TerminalScanDetailModel detail) {

        TerminalScanDetailModel updateDetail = new TerminalScanDetailModel();
        updateDetail.setId(detail.getId());
        updateDetail.setVirtualAmount(recordModel.getIntegral());
        updateDetail.setActivityId(recordModel.getActivityId());
        terminalScanDetailPlusDao.updateById(updateDetail);

        recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
        recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
        recordModel.setUpdateTime(new Date());
        activityRewardRecordDao.updateById(recordModel);

        // 发送消息通知终端账户处理积分
        sendTerminalAccountScoreMessage(recordModel);

    }

    private TerminalRewardRecordSourceEnum getTerminalRewardRecordSourceEnum(ActivityRewardRecordModel recordModel) {
        if (Objects.equals(recordModel.getRewardSubType(), ActivityRewardSubTypeEnum.OPEN_BOTTLE_STIMULATE_REWARD.getCode())) {
            if (Objects.equals(recordModel.getEventType(), EventType.TERMINAL_BANQUET_OPEN_BOTTLE_REWARD_DEDUCT.getCode())) {
                return TerminalRewardRecordSourceEnum.BANQUET_OPEN_BOTTLE_STIMULATE_REWARD_DEDUCT;
            }
            if (Objects.equals(recordModel.getEventType(), EventType.TERMINAL_OPEN_BOTTLE_REWARD_OPERATIONAL_DEDUCT.getCode())) {
                return TerminalRewardRecordSourceEnum.OPEN_BOTTLE_STIMULATE_REWARD_OPERATIONAL_DEDUCT;
            }
            return TerminalRewardRecordSourceEnum.OPEN_BOTTLE_STIMULATE_REWARD;
        }
        if (Objects.equals(recordModel.getEventType(), EventType.DISTRIBUTOR_OPEN_BOTTLE_REWARD_OPERATIONAL_REISSUE.getCode())) {
            return TerminalRewardRecordSourceEnum.OPEN_BOTTLE_REWARD_OPERATIONAL_REISSUE;
        }
        return TerminalRewardRecordSourceEnum.OPEN_BOTTLE_REWARD;
    }

    @Transactional(rollbackFor = Exception.class)
    public void handleDistributorBottleReward(ActivityRewardRecordModel recordModel, TerminalScanDetailModel
            detail) {

        recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
        recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
        recordModel.setUpdateTime(new Date());
        activityRewardRecordDao.updateById(recordModel);

        sendTerminalAccountScoreMessage(recordModel);

    }

    @Transactional
    public void handleDealerSaleReward(List<ActivityRewardRecordModel> dealerRecordList) {
        if (!CollectionUtils.isEmpty(dealerRecordList)) {
            for (ActivityRewardRecordModel recordModel : dealerRecordList) {
                String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_SALE_DEALER_KEY, recordModel.getId());
                Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, recordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
                if (!aBoolean) {
                    log.info("已处理过奖励，不能重复处理:{}", JSONObject.toJSONString(recordModel));
                    return;
                }
                LambdaQueryWrapper<CloudDealerRewardRecordModel> clqw = Wrappers.lambdaQuery();
                clqw.eq(CloudDealerRewardRecordModel::getOrderCode, recordModel.getOrderCode());
                clqw.eq(CloudDealerRewardRecordModel::getQrCode, recordModel.getCode());
                if(recordModel.getEventType().equals(EventType.DEALER_SALE_REWARD.getCode())){ // 正常动销 +
                    clqw.eq(CloudDealerRewardRecordModel::getDetailId, recordModel.getOriginId());
                } else { // 异地动销  +-
                    clqw.eq(CloudDealerRewardRecordModel::getOriginId, recordModel.getId());
                }
                clqw.eq(CloudDealerRewardRecordModel::getOriginType, 1);
                clqw.eq(CloudDealerRewardRecordModel::getIsDelete, 0);
                clqw.last(" LIMIT 1");
                CloudDealerRewardRecordModel cloudDealerRewardRecordModel = cloudDealerRewardRecordDao.selectOne(clqw);
                if (Objects.nonNull(cloudDealerRewardRecordModel)) {
                    boolean alreadyExisted = checkExistedDuplicateEventType(recordModel);

                    if (alreadyExisted) {
                        recordModel.setSysState(SysState.SEND_SUCCESS.getCode());
                        recordModel.setSendMsg(recordModel.getSendMsg() + ",此码重复发放奖励:" + DateUtils.convert2StringYYYYMMddHHmmss(new Date()));
                        activityRewardRecordDao.updateById(recordModel);
                        return;
                    }
                }

                // 解析扩展字段
                ExtendDataBean extendDataBean = null;
                if (StringUtils.isNotEmpty(recordModel.getExtendData())) {
                    extendDataBean = JSONObject.parseObject(recordModel.getExtendData(), ExtendDataBean.class);
                } else {
                    extendDataBean = new ExtendDataBean();
                }

                CloudDealerRewardRecordModel insertData = new CloudDealerRewardRecordModel();
                if(recordModel.getEventType().equals(EventType.DEALER_SALE_REWARD.getCode())){
                    TerminalScanDetailModel detail = terminalScanDetailPlusDao.selectById(recordModel.getOriginId());
                    //组装数据
                    if (Objects.nonNull(extendDataBean)) {
                        String activityName = extendDataBean.getActivityName();
                        insertData.setActivityName(activityName);
                        insertData.setGoodCode(extendDataBean.getGoodsCode());
                        insertData.setGoodName(extendDataBean.getGoodsName());
                        insertData.setDealerCode(extendDataBean.getDealerCode());
                        insertData.setShopId(extendDataBean.getDeliveryShopId());
                        insertData.setUnitPriceOfRewardAmount(extendDataBean.getUnitAmount());
                    }
                    insertData.setGoodNumber(detail.getQuantity());
                    insertData.setOriginId(Long.valueOf(detail.getBalanceId()));
                    insertData.setOriginType(1);
                    insertData.setQrCode(detail.getQrcode());
                    insertData.setActivityId(recordModel.getActivityId());
                    insertData.setRewardType(1);
                    insertData.setRewardAmount(recordModel.getIntegral());
                    insertData.setOrderCode(recordModel.getOrderCode());
                    insertData.setIsDelete(0);
                    insertData.setCreateTime(new Date());
                    insertData.setCompanyId(detail.getCompanyId());
                    insertData.setDetailId(detail.getId());
                    insertData.setStatus(1);
                    cloudDealerRewardRecordDao.insert(insertData);
                    recordModel.setRewardId(insertData.getId());
                } else {
                    if (Objects.nonNull(extendDataBean)) {
                        String activityName = extendDataBean.getActivityName();
                        insertData.setActivityName(activityName);
                        insertData.setGoodCode(extendDataBean.getGoodsCode());
                        insertData.setGoodName(extendDataBean.getGoodsName());
                        insertData.setDealerCode(extendDataBean.getDealerCode());
                        insertData.setShopId(extendDataBean.getDeliveryShopId());
                        insertData.setUnitPriceOfRewardAmount(extendDataBean.getUnitAmount());
                        insertData.setDetailId(extendDataBean.getScanDetailId());
                    }
                    insertData.setGoodNumber(1);
                    insertData.setOriginId(Long.valueOf(recordModel.getId()));
                    insertData.setOriginType(1);
                    insertData.setQrCode(recordModel.getCode());
                    insertData.setActivityId(recordModel.getActivityId());
                    insertData.setRewardType(1);
                    insertData.setRewardAmount(recordModel.getIntegral());
                    insertData.setOrderCode(recordModel.getOrderCode());
                    insertData.setIsDelete(0);
                    insertData.setCreateTime(new Date());
                    insertData.setCompanyId(recordModel.getCompanyId());
                    insertData.setStatus(1);
                    cloudDealerRewardRecordDao.insert(insertData);
                }

                recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
                recordModel.setSendStatus(SendStatus.SUCCESS_TYPE.getCode());
                recordModel.setSendMsg("发放奖励成功:" + DateUtils.convert2StringYYYYMMddHHmmss(new Date()));
                extendDataBean.setOldRewardId(insertData.getId());
                recordModel.setRewardId(insertData.getId());
                recordModel.setExtendData(JSONObject.toJSONString(extendDataBean));
                activityRewardRecordDao.updateById(recordModel);


                ZtBookRewardRecordModel ztBookRewardRecordModel = ztBookRewardRecordService.getOne(new QueryWrapper<ZtBookRewardRecordModel>().lambda().eq(ZtBookRewardRecordModel::getBusinessId, recordModel.getBusinessId()).eq(ZtBookRewardRecordModel::getCallState, 1).eq(ZtBookRewardRecordModel::getIsDelete, 0).orderByDesc(ZtBookRewardRecordModel::getCreateTime).last("limit 1"));
                if (Objects.nonNull(ztBookRewardRecordModel)) {
                    ztBookRewardRecordModel.setCallState(2);
                    ztBookRewardRecordModel.setUpdateTime(new Date());
                    ztBookRewardRecordService.updateById(ztBookRewardRecordModel);
                }
            }
        }
    }

    private boolean checkExistedDuplicateEventType(ActivityRewardRecordModel recordModel) {
        // 检查事件类型是否重复
        Long count = activityRewardRecordDao.selectCount(
                Wrappers.lambdaQuery(ActivityRewardRecordModel.class)
                        .eq(ActivityRewardRecordModel::getOriginId, recordModel.getOriginId())
                        .eq(ActivityRewardRecordModel::getActivityType, recordModel.getActivityType())
                        .eq(ActivityRewardRecordModel::getEventType, recordModel.getEventType())
                        .ne(ActivityRewardRecordModel::getId, recordModel.getId())
                        .eq(ActivityRewardRecordModel::getIsDelete, 0)
        );

        if (count > 0) {
            log.info("检测到重复事件类型，originId={}, eventType={}, count={}", recordModel.getOriginId(), recordModel.getEventType(), count);
            return true;
        }
        return false;
    }

    @Transactional
    public void handleDistributorSaleReward(List<ActivityRewardRecordModel> distributorRecordList) {
        if (!CollectionUtils.isEmpty(distributorRecordList)) {
            for (ActivityRewardRecordModel recordModel : distributorRecordList) {
                String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_SALE_DISTRIBUTOR_KEY, recordModel.getId());
                Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, recordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
                if (!aBoolean) {
                    log.info("已处理过奖励，不能重复处理:{}", JSONObject.toJSONString(recordModel));
                    return;
                }

                recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
                recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
                recordModel.setUpdateTime(new Date());
                activityRewardRecordDao.updateById(recordModel);

                sendTerminalAccountScoreMessage(recordModel);
            }
        }
    }

    @Transactional
    public void handleTerminalSaleReward(List<ActivityRewardRecordModel> terminalRecordList, Boolean isMember) {
        if (!CollectionUtils.isEmpty(terminalRecordList)) {
            for (ActivityRewardRecordModel recordModel : terminalRecordList) {
                String key = String.format(RedisConstant.ACTIVITY_REWARD_RECORD_SALE_TERMINAL_KEY, recordModel.getId());
                Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, recordModel.getId(), RedisConstant.REDIS_TIME_OUT_FIVE, TimeUnit.MINUTES);
                if (!aBoolean) {
                    log.info("已处理过奖励，不能重复处理:{}", JSONObject.toJSONString(recordModel));
                    return;
                }


                if(recordModel.getEventType().equals(EventType.DEALER_SALE_REWARD.getCode())){
                    TerminalScanDetailModel detail = terminalScanDetailPlusDao.selectById(recordModel.getOriginId());

                    TerminalScanDetailModel updateDetail = new TerminalScanDetailModel();
                    updateDetail.setId(detail.getId());
                    updateDetail.setVirtualAmount(recordModel.getIntegral());
                    updateDetail.setActivityId(recordModel.getActivityId());
                    terminalScanDetailPlusDao.updateById(updateDetail);
                }

//                shopDao.addVirtualAmount(model.getAmount(), model.getShopId());

                recordModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
                recordModel.setSendStatus(SendStatus.DOING_TYPE.getCode());
                recordModel.setUpdateTime(new Date());
                activityRewardRecordDao.updateById(recordModel);

                sendTerminalAccountScoreMessage(recordModel);
            }
        }
    }

    /**
     * 同一个码被重复收货且未退 不发奖励
     */
    private Boolean getJudgeTerminal(TerminalScanDetailModel detail) {
        TerminalScanDetailModel detailModel = terminalScanDetailPlusDao.hasJudgeTerminalRrwordByQrCode(detail.getId(), detail.getQrcode());
        if (Objects.nonNull(detailModel)) {
            RewardReason rewardReason = initRewardReason(detailModel, 0);
            rewardReason.setIsSave(true);
            rewardReason.setHasTerminal(0);
            String msg = "码:" + detail.getQrcode() + "被" + detailModel.getReceivedOrderCode() + "收过货且已发奖励";
            rewardReason.setTerminalDesc(msg);
            if (rewardReason.getIsSave()) {
                rewardReasonDao.insert(rewardReason);
            }
            return true;
        }
        return false;
    }



    private RewardReason initRewardReason(TerminalScanDetailModel detail, Integer receiptType) {
        RewardReason rewardReason = new RewardReason();
        rewardReason.setReceiptType(receiptType);
        rewardReason.setDetailId(detail.getId());
        rewardReason.setShopId(detail.getShopId());
        rewardReason.setAccountType(detail.getAccountType());
        rewardReason.setRewardType(0);
        rewardReason.setOrderCode(detail.getReceivedOrderCode());
        rewardReason.setQrCode(detail.getQrcode());
        rewardReason.setIsSave(false);
        rewardReason.setCreateTime(new Date());
        rewardReason.setOrderTime(detail.getCreateTime());
        return rewardReason;
    }

    /**
     * 发送消息通知终端账户处理积分增加或减少
     *
     * @param recordModel 活动奖励记录模型
     */
    private void sendTerminalAccountScoreMessage(ActivityRewardRecordModel recordModel) {
        if (Objects.isNull(recordModel)) {
            log.warn("无法发送终端账户消息：参数不完整");
            return;
        }
        AccountScoreSendReq accountScoreSendReq = null;
        try {
            // 构建AccountScoreSendReq消息对象
            accountScoreSendReq = AccountScoreSendReq.fromRewardRecord(recordModel)
                    .withProductCategoryService(productCategoryService)
                    .withTerminalScanDetailPlusDao(terminalScanDetailPlusDao)
                    .withConsumerScanDetailCommonService(consumerScanDetailCommonService)
                    .build();
            // 使用新的终端积分账户客户端发送消息
            log.info("开始发送终端账户积分处理消息，业务单号: {}, 事项来源类型: {},积分: {}, 产品: {}, 产品品类: {}", accountScoreSendReq.getBusinessNo(),
                    accountScoreSendReq.getTransactionSource(), accountScoreSendReq.getPoints(),
                    Optional.ofNullable(accountScoreSendReq.getProduct()).orElse(Collections.emptyList()),
                    Optional.ofNullable(accountScoreSendReq.getExt().getProductCategories()).orElse(Collections.emptyList()));

            terminalScoreAccountClient.sendMessage(accountScoreSendReq);
            log.info("终端账户积分处理消息发送成功，业务单号: {}", accountScoreSendReq.getBusinessNo());
        } catch (Exception e) {
            String businessNo = Optional.ofNullable(accountScoreSendReq).map(AccountScoreSendReq::getBusinessNo)
                    .orElse("未知");
            log.error("发送终端账户积分处理消息失败， 业务单号: {}, 原因: {} ", businessNo, e.getMessage(), e);
            updateRecordStatusOnFailure(recordModel, e.getMessage());
        }
    }

    /**
     * 消息发送失败时更新记录状态
     *
     * @param recordModel 活动奖励记录模型，不能为null
     */
    private void updateRecordStatusOnFailure(ActivityRewardRecordModel recordModel, String reason) {
        if (recordModel == null) {
            log.error("更新记录状态失败：参数为空");
            return;
        }

        try {
            // 更新活动奖励记录状态
            updateActivityRewardRecord(recordModel, reason);
            // 更新账本记录状态
            processZtBookRecordFailure(recordModel, reason);
        } catch (Exception e) {
            log.error("更新奖励记录状态失败，记录ID: {}, 错误原因: {}", recordModel.getId(), e.getMessage(), e);
        }
    }

    /**
     * 更新活动奖励记录状态
     *
     * @param recordModel 活动奖励记录模型
     */
    private void updateActivityRewardRecord(ActivityRewardRecordModel recordModel, String reason) {
        ActivityRewardRecordModel updateModel = new ActivityRewardRecordModel();
        updateModel.setId(recordModel.getId());
        updateModel.setSendStatus(SendStatus.FAIL_TYPE.getCode());
        updateModel.setSysState(SysState.ZT_HANDLE_SUCCESS.getCode());
        String sendMsg = "发放奖励失败:" + reason + ":" + DateUtils.convert2StringYYYYMMddHHmmss(new Date());
        updateModel.setSendMsg(sendMsg.substring(0, Math.min(sendMsg.length(), 250)));

        try {
            activityRewardRecordDao.updateById(updateModel);
            log.info("活动奖励记录状态已更新为失败，记录ID: {}", recordModel.getId());
        } catch (JSONException e) {
            log.error("活动奖励记录状态更新失败 - JSON解析异常，记录ID: {}, 原因: {}", recordModel.getId(), e.getMessage());
            throw e;
        } catch (DataAccessException e) {
            log.error("活动奖励记录状态更新失败 - 数据访问异常，记录ID: {}, 原因: {}", recordModel.getId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 处理中台预算对账流水表记录失败状态更新
     *
     * @param recordModel 活动奖励记录模型，包含业务ID
     */
    private void processZtBookRecordFailure(ActivityRewardRecordModel recordModel, String reason) {
        if (recordModel == null || recordModel.getBusinessId() == null) {
            log.warn("无法处理中台预算对账流水表记录：业务ID为空");
            return;
        }

        try {
            Optional<ZtBookRewardRecordModel> optionalRecord = ztBookRewardRecordService.findPendingRecordByBusinessId(recordModel.getBusinessId());

            if (!optionalRecord.isPresent()) {
                log.debug("未找到对应的待处理中台预算对账流水表记录，业务ID: {}", recordModel.getBusinessId());
                return;
            }

            ZtBookRewardRecordModel ztBookRecord = optionalRecord.get();
            ZtBookRewardRecordModel updateRecord = new ZtBookRewardRecordModel();
            updateRecord.setId(ztBookRecord.getId());
            updateRecord.setCallState(CallStateEnum.PROCESS_FAIL.getKey());
            updateRecord.setUpdateTime(new Date());

            boolean updated = ztBookRewardRecordService.updateById(updateRecord);
            if (updated) {
                log.info("中台预算对账流水表记录状态已更新为处理失败，记录ID: {}, 业务ID: {}, 原因: {}",
                        ztBookRecord.getId(), recordModel.getBusinessId(), reason);
            } else {
                log.warn("中台预算对账流水表记录状态更新失败，记录ID: {}, 业务ID: {}, 原因: {}",
                        ztBookRecord.getId(), recordModel.getBusinessId(), reason);
            }
        } catch (Exception e) {
            log.error("处理中台预算对账流水表记录失败状态更新异常，业务ID: {}, 原因: {}",
                    recordModel.getBusinessId(), e.getMessage());
            throw e;
        }
    }
}
